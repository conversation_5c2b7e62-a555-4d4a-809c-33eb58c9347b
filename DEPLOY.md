# 部署指南

本指南将帮助您将Yupp.ai OpenAI API适配器部署到各种平台。

## 🚀 Deno Deploy (推荐)

Deno Deploy是最简单的部署方式，完全免费且支持全球CDN。

### 步骤 1: 准备代码

1. 确保你有 `yupp2api.ts` 文件
2. 根据需要修改配置（API密钥、Yupp令牌等）

### 步骤 2: 部署到Deno Deploy

#### 方法A: 通过GitHub (推荐)

1. 将代码推送到GitHub仓库
2. 访问 [Deno Deploy](https://deno.com/deploy)
3. 点击 "New Project"
4. 连接你的GitHub仓库
5. 选择 `yupp2api.ts` 作为入口文件
6. 点击 "Deploy"

#### 方法B: 直接上传

1. 访问 [Deno Deploy](https://deno.com/deploy)
2. 点击 "New Project"
3. 选择 "Upload files"
4. 上传 `yupp2api.ts` 文件
5. 设置入口文件为 `yupp2api.ts`
6. 点击 "Deploy"

### 步骤 3: 配置环境变量 (可选)

在Deno Deploy项目设置中，你可以添加环境变量：

- `DEBUG_MODE`: 设置为 `true` 启用调试模式

### 步骤 4: 获取部署URL

部署完成后，你会得到一个类似这样的URL：
```
https://your-project-name.deno.dev
```

## 🐳 Docker 部署

如果你想使用Docker部署，可以创建以下Dockerfile：

```dockerfile
FROM denoland/deno:1.40.0

WORKDIR /app

# 复制源代码
COPY yupp2api.ts .

# 缓存依赖
RUN deno cache yupp2api.ts

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["run", "-A", "yupp2api.ts"]
```

构建和运行：

```bash
# 构建镜像
docker build -t yupp2api .

# 运行容器
docker run -p 8000:8000 yupp2api
```

## ☁️ 其他云平台

### Vercel

1. 安装Vercel CLI: `npm i -g vercel`
2. 在项目根目录创建 `vercel.json`:

```json
{
  "functions": {
    "api/[...path].ts": {
      "runtime": "deno"
    }
  },
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/api/$1"
    }
  ]
}
```

3. 将 `yupp2api.ts` 移动到 `api/[...path].ts`
4. 运行 `vercel --prod`

### Railway

1. 连接GitHub仓库到Railway
2. 添加环境变量 `PORT=8000`
3. Railway会自动检测Deno项目并部署

### Heroku

1. 创建 `Procfile`:
```
web: deno run -A --port=$PORT yupp2api.ts
```

2. 修改 `yupp2api.ts` 中的端口配置:
```typescript
const port = parseInt(Deno.env.get("PORT") || "8000");
serve(handleRequest, { port });
```

3. 使用Heroku CLI部署

## 🔧 配置说明

### 必需配置

在部署前，请确保修改以下配置：

1. **客户端API密钥** (`VALID_CLIENT_KEYS`)
   ```typescript
   const VALID_CLIENT_KEYS = new Set<string>([
     "sk-your-secure-key-here",
   ]);
   ```

2. **Yupp账户令牌** (`YUPP_ACCOUNTS`)
   ```typescript
   const YUPP_ACCOUNTS: YuppAccount[] = [
     {
       token: "your-actual-yupp-session-token",
       is_valid: true,
       last_used: 0,
       error_count: 0,
     },
   ];
   ```

### 可选配置

- **调试模式**: 设置环境变量 `DEBUG_MODE=true`
- **端口**: 大多数平台会自动设置，无需修改

## 🧪 测试部署

部署完成后，使用以下命令测试：

```bash
# 测试模型列表
curl -H "Authorization: Bearer your-api-key" \
     https://your-deployment-url.com/v1/models

# 测试聊天
curl -X POST https://your-deployment-url.com/v1/chat/completions \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Claude Sonnet 4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

或者使用提供的测试脚本：

```bash
# 修改test.ts中的BASE_URL为你的部署URL
deno run -A test.ts
```

## 🔒 安全建议

1. **使用强密钥**: 生成复杂的客户端API密钥
2. **定期轮换**: 定期更新Yupp会话令牌
3. **监控使用**: 启用调试模式监控API使用情况
4. **限制访问**: 考虑添加IP白名单或其他访问控制

## 📊 监控和日志

- 使用 `/debug?enable=true` 启用详细日志
- 大多数云平台提供内置的日志查看功能
- 考虑集成第三方监控服务（如Sentry）

## 🆘 故障排除

### 常见问题

1. **401/403错误**: 检查API密钥配置
2. **503错误**: 检查Yupp会话令牌是否有效
3. **超时错误**: 检查网络连接和Yupp服务状态
4. **模型不可用**: 确认模型名称正确

### 调试步骤

1. 启用调试模式查看详细日志
2. 检查环境变量配置
3. 验证Yupp账户状态
4. 测试网络连接

## 📞 支持

如果遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看项目的GitHub Issues
3. 提交新的Issue并提供详细信息
