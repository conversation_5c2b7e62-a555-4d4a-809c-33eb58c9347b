/**
 * 配置示例文件
 * 复制此文件并根据你的需求修改配置
 */

// 客户端API密钥配置
// 这些密钥用于控制谁可以访问你的API
export const VALID_CLIENT_KEYS = new Set<string>([
  "sk-your-secure-api-key-1",
  "sk-your-secure-api-key-2",
  // 添加更多密钥...
]);

// Yupp账户配置
// 从Yupp.ai网站获取会话令牌
export const YUPP_ACCOUNTS = [
  {
    token: "your-yupp-session-token-1",
    is_valid: true,
    last_used: 0,
    error_count: 0,
  },
  {
    token: "your-yupp-session-token-2", 
    is_valid: true,
    last_used: 0,
    error_count: 0,
  },
  // 添加更多账户以提高可用性...
];

// 模型配置
// 根据你的Yupp账户可用的模型进行配置
export const AVAILABLE_MODELS = [
  {
    id: "Claude Opus 4",
    name: "claude-opus-4-********",
    label: "Claude Opus 4",
    owned_by: "anthropic",
  },
  {
    id: "Claude Sonnet 4",
    name: "claude-sonnet-4-********", 
    label: "Claude Sonnet 4",
    owned_by: "anthropic",
  },
  {
    id: "Claude 3.7 Sonnet (Thinking)",
    name: "claude-3-7-sonnet-********<>thinking",
    label: "Claude 3.7 Sonnet (Thinking)",
    owned_by: "anthropic",
  },
  {
    id: "GPT-4o",
    name: "gpt-4o",
    label: "GPT-4o", 
    owned_by: "openai",
  },
  // 添加更多模型...
];

// 其他配置选项
export const CONFIG = {
  // 最大错误次数，超过后账户将被暂时禁用
  MAX_ERROR_COUNT: 3,
  
  // 错误冷却时间（毫秒），账户出错后的等待时间
  ERROR_COOLDOWN: 300000, // 5分钟
  
  // 默认调试模式
  DEBUG_MODE: false,
  
  // 服务器端口
  PORT: 8000,
};

/**
 * 如何获取Yupp会话令牌：
 * 
 * 1. 登录到 https://yupp.ai
 * 2. 打开浏览器开发者工具 (F12)
 * 3. 转到 Application/Storage > Cookies
 * 4. 找到 __Secure-yupp.session-token 的值
 * 5. 复制该值作为token使用
 * 
 * 注意：会话令牌可能会过期，需要定期更新
 */

/**
 * 如何生成安全的API密钥：
 * 
 * 1. 使用强随机字符串生成器
 * 2. 建议格式：sk-[32位随机字符]
 * 3. 示例：sk-1234567890abcdef1234567890abcdef
 * 4. 定期轮换密钥以提高安全性
 */

/**
 * 模型名称映射说明：
 * 
 * - id: 客户端使用的模型标识符
 * - name: Yupp.ai内部使用的模型名称
 * - label: 显示给用户的友好名称
 * - owned_by: 模型提供商（anthropic/openai等）
 */
