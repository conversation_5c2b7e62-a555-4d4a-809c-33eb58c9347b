@echo off
REM Yupp.ai OpenAI API Adapter 启动脚本 (Windows)

echo 🚀 启动 Yupp.ai OpenAI API Adapter...

REM 检查Deno是否已安装
deno --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Deno未安装，请先安装Deno:
    echo    https://deno.land/manual/getting_started/installation
    pause
    exit /b 1
)

REM 检查主文件是否存在
if not exist "yupp2api.ts" (
    echo ❌ 找不到 yupp2api.ts 文件
    pause
    exit /b 1
)

REM 设置环境变量
if not defined DEBUG_MODE set DEBUG_MODE=false
if not defined PORT set PORT=8000

echo 📋 配置信息:
echo    调试模式: %DEBUG_MODE%
echo    端口: %PORT%
echo    文件: yupp2api.ts

REM 启动服务器
echo 🌟 启动服务器...
deno run -A yupp2api.ts

pause
