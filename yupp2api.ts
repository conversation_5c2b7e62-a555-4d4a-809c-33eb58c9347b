/****************************************************************************************
 *  Yupp.ai OpenAI API Adapter (Deno edition)
 *  -------------------------------------------------------------------------------------
 *  TypeScript version for Deno deployment
 *  Licensed under the Affero Public License – APGL-3.0
 *
 *  ▸ End-points
 *      GET  /v1/models                 (requires "Bearer <client-key>")
 *      GET  /models                    (no auth)
 *      POST /v1/chat/completions       (requires auth, supports stream=true|false)
 *      GET  /debug?enable=true|false   (toggle verbose debug log)
 *
 *  ▸ How to run
 *      deno run -A yupp2api.ts
 *****************************************************************************************/

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";

// ----------------------------------------------------------------------------
// Hard-coded configuration  (replace with your own data)
// ----------------------------------------------------------------------------
const VALID_CLIENT_KEYS = new Set<string>([
  // Client API keys that callers must present in Authorization: Bearer <key>
  "sk-dummy-sdfancy",
]);

type YuppAccount = {
  token: string;
  is_valid: boolean;
  last_used: number;
  error_count: number;
};

const YUPP_ACCOUNTS: YuppAccount[] = [
  {
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6Im1qa3psNzZ1emk3ejd1bGF3bHVvY3ljbXVnbzdtNnFuIiwib25ib2FyZGluZ1N0YXR1cyI6IlVJX1NUQVRFX05PVF9TVEFSVEVEIiwicm9sZSI6InVzZXIiLCJ2ZXJzaW9uIjoxfQ.DEeBWBqtwVKIyr3NfCGut05M2VrT_20r5uszKMGHpK8",
    is_valid: true,
    last_used: 0,
    error_count: 0,
  },
  {
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6IjN3bXFzdmd2c3F1bHdpZDZ5NHprb3R4aGxvcW41YWJqIiwicm9sZSI6InVzZXIiLCJvbmJvYXJkaW5nU3RhdHVzIjoiVUlfU1RBVEVfQ09NUExFVEVEIiwidmVyc2lvbiI6MX0.J735hP_5SPKNLplZ1g6WyChQkemIrfvEMKh38S1Ih2U",
    is_valid: true,
    last_used: 0,
    error_count: 0,
  },
];

// ----------------------------------------------------------------------------
// Types that mimic OpenAI-style payloads (only the fields we need).
// ----------------------------------------------------------------------------
type ChatMessage = { 
  role: "user" | "assistant" | "system"; 
  content: string;
  reasoning_content?: string;
};

type ChatCompletionRequest = {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  raw_response?: boolean;
};

// Model registry
type ModelRecord = {
  id: string;            // e.g. "Claude Opus 4"
  name: string;          // yupp model name
  label: string;         // display name
  owned_by: string;      // "anthropic" | "openai"
};

const AVAILABLE_MODELS: ModelRecord[] = [
  {
    id: "Claude Opus 4",
    name: "claude-opus-4-20250514",
    label: "Claude Opus 4",
    owned_by: "anthropic",
  },
  {
    id: "Claude Opus 4 (Amazon Bedrock)",
    name: "us.anthropic.claude-opus-4-20250514-v1:0<>BED",
    label: "Claude Opus 4 (Amazon Bedrock)",
    owned_by: "anthropic",
  },
  {
    id: "Claude Opus 4 (OpenRouter)",
    name: "anthropic/claude-opus-4<>OPR",
    label: "Claude Opus 4 (OpenRouter)",
    owned_by: "anthropic",
  },
  {
    id: "Claude Sonnet 4",
    name: "claude-sonnet-4-20250514",
    label: "Claude Sonnet 4",
    owned_by: "anthropic",
  },
  {
    id: "Claude Sonnet 4 (OpenRouter)",
    name: "anthropic/claude-sonnet-4<>OPR",
    label: "Claude Sonnet 4 (OpenRouter)",
    owned_by: "anthropic",
  },
  {
    id: "Claude 3.7 Sonnet (Thinking)",
    name: "claude-3-7-sonnet-20250219<>thinking",
    label: "Claude 3.7 Sonnet (Thinking)",
    owned_by: "anthropic",
  },
  {
    id: "GPT-4o",
    name: "gpt-4o",
    label: "GPT-4o",
    owned_by: "openai",
  },
  {
    id: "GPT-4.5 Preview (Azure)",
    name: "gpt-4.5-preview-west2<>AZR",
    label: "GPT-4.5 Preview (Azure)",
    owned_by: "openai",
  },
];

// ----------------------------------------------------------------------------
// House-keeping
// ----------------------------------------------------------------------------
let DEBUG_MODE = false;
const MAX_ERROR_COUNT = 3;
const ERROR_COOLDOWN = 300000; // 5 minutes

function logDebug(msg: unknown) {
  if (DEBUG_MODE) console.log(`[DEBUG]`, msg);
}

// ----------------------------------------------------------------------------
// Yupp API helpers
// ----------------------------------------------------------------------------
function formatMessagesForYupp(messages: ChatMessage[]): string {
  const formatted: string[] = [];
  
  // 处理系统消息
  const systemMessages = messages.filter(msg => msg.role === "system");
  if (systemMessages.length > 0) {
    for (const sysMsg of systemMessages) {
      formatted.push(sysMsg.content);
    }
  }
  
  // 处理用户和助手消息
  const userAssistantMsgs = messages.filter(msg => msg.role !== "system");
  for (const msg of userAssistantMsgs) {
    const role = msg.role === "user" ? "Human" : "Assistant";
    formatted.push(`\n\n${role}: ${msg.content}`);
  }
  
  // 确保以Assistant:结尾
  if (formatted.length === 0 || !formatted[formatted.length - 1].trim().startsWith("Assistant:")) {
    formatted.push("\n\nAssistant:");
  }
  
  let result = formatted.join("");
  // 如果以\n\n开头，则删除
  if (result.startsWith("\n\n")) {
    result = result.slice(2);
  }
  
  return result;
}

function getBestYuppAccount(): YuppAccount | undefined {
  const now = Date.now();
  const validAccounts = YUPP_ACCOUNTS.filter(acc => {
    if (!acc.is_valid) return false;
    if (acc.error_count >= MAX_ERROR_COUNT && now - acc.last_used < ERROR_COOLDOWN) {
      return false;
    }
    return true;
  });
  
  if (validAccounts.length === 0) return undefined;
  
  // Reset error count for accounts that have been in cooldown
  for (const acc of validAccounts) {
    if (acc.error_count >= MAX_ERROR_COUNT && now - acc.last_used > ERROR_COOLDOWN) {
      acc.error_count = 0;
    }
  }
  
  // Sort by last used (oldest first) and error count (lowest first)
  validAccounts.sort((a, b) => (a.last_used - b.last_used) || (a.error_count - b.error_count));
  const account = validAccounts[0];
  account.last_used = now;
  return account;
}

function generateUUID(): string {
  return crypto.randomUUID();
}

async function claimYuppReward(account: YuppAccount, rewardId: string): Promise<number | null> {
  try {
    logDebug(`Claiming reward ${rewardId}...`);
    const url = "https://yupp.ai/api/trpc/reward.claim?batch=1";
    const payload = { "0": { "json": { "rewardId": rewardId } } };
    const headers = {
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "Content-Type": "application/json",
      "sec-fetch-site": "same-origin",
      "Cookie": `__Secure-yupp.session-token=${account.token}`,
    };

    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) throw new Error(`HTTP ${response.status}`);

    const data = await response.json();
    const balance = data[0]?.result?.data?.json?.currentCreditBalance;
    console.log(`Reward claimed successfully. New balance: ${balance}`);
    return balance;
  } catch (err) {
    console.error(`Failed to claim reward ${rewardId}. Error:`, err);
    return null;
  }
}

// ----------------------------------------------------------------------------
// Utility logic
// ----------------------------------------------------------------------------
function requireAuth(req: Request): string {
  const header = req.headers.get("Authorization") ?? "";
  const m = header.match(/^Bearer\s+(.+)$/i);
  if (!m) throw new Error("401");
  const token = m[1].trim();
  if (!VALID_CLIENT_KEYS.has(token)) throw new Error("403");
  return token;
}

function jsonResponse(obj: unknown, status = 200) {
  return new Response(JSON.stringify(obj), {
    status,
    headers: { "Content-Type": "application/json" },
  });
}

// Streaming helpers
function sseStream(
  gen: AsyncGenerator<string>,
  status = 200,
): Response {
  const stream = new ReadableStream({
    async start(ctrl) {
      try {
        for await (const chunk of gen) {
          ctrl.enqueue(new TextEncoder().encode(chunk));
        }
        ctrl.close();
      } catch (e) {
        ctrl.error(e);
      }
    },
  });
  return new Response(stream, {
    status,
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    },
  });
}

// Stream generators
async function* yuppStreamGenerator(
  responseLines: AsyncIterable<Uint8Array>,
  modelId: string,
  account: YuppAccount,
) {
  const streamId = crypto.randomUUID();
  const created = Math.floor(Date.now() / 1000);
  const header = JSON.stringify({
    id: streamId,
    object: "chat.completion.chunk",
    created,
    model: modelId,
    choices: [{ delta: { role: "assistant" }, index: 0 }],
  });
  yield `data: ${header}\n\n`;

  const linePattern = /^([0-9a-fA-F]+):(.*)/;
  const chunks: Record<string, any> = {};
  let targetStreamId: string | null = null;
  let rewardInfo: any = null;
  let isThinking = false;
  let thinkingContent = "";
  let normalContent = "";

  function extractRefId(ref: string | undefined): string | null {
    return ref && typeof ref === "string" && ref.startsWith("$@") ? ref.slice(2) : null;
  }

  try {
    const decoder = new TextDecoder();
    for await (const chunk of responseLines) {
      const lines = decoder.decode(chunk).split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;

        const match = line.match(linePattern);
        if (!match) continue;

        const [, chunkId, chunkData] = match;

        let data: any;
        try {
          data = chunkData === "{}" ? {} : JSON.parse(chunkData);
          chunks[chunkId] = data;
        } catch {
          continue;
        }

        // 处理奖励信息
        if (chunkId === "a") {
          rewardInfo = data;
        }
        // 处理初始设置信息
        else if (chunkId === "1") {
          // 存储流信息供后续使用
          chunks[chunkId] = data;
        }
        else if (chunkId === "e") {
          const modelSelections = data.modelSelections || [];
          for (let i = 0; i < modelSelections.length; i++) {
            const selection = modelSelections[i];
            if (selection.selectionSource === "USER_SELECTED") {
              const selectStream = chunks["1"] ? [chunks["1"].leftStream || {}, chunks["1"].rightStream || {}] : [{}, {}];
              targetStreamId = extractRefId(selectStream[i]?.next);
              break;
            }
          }
        }
        // 处理目标流内容
        else if (targetStreamId && chunkId === targetStreamId) {
          const content = data.curr || "";
          if (content) {
            // 处理思考过程
            if (content.includes("<think>")) {
              const parts = content.split("<think>", 2);
              if (parts[0]) {
                normalContent += parts[0];
                const contentChunk = JSON.stringify({
                  id: streamId,
                  object: "chat.completion.chunk",
                  created,
                  model: modelId,
                  choices: [{ delta: { content: parts[0] }, index: 0 }],
                });
                yield `data: ${contentChunk}\n\n`;
              }

              isThinking = true;
              const thinkingPart = parts[1];

              if (thinkingPart.includes("</think>")) {
                const thinkParts = thinkingPart.split("</think>", 2);
                thinkingContent += thinkParts[0];
                const reasoningChunk = JSON.stringify({
                  id: streamId,
                  object: "chat.completion.chunk",
                  created,
                  model: modelId,
                  choices: [{ delta: { reasoning_content: thinkParts[0] }, index: 0 }],
                });
                yield `data: ${reasoningChunk}\n\n`;

                isThinking = false;
                if (thinkParts[1]) {
                  normalContent += thinkParts[1];
                  const contentChunk = JSON.stringify({
                    id: streamId,
                    object: "chat.completion.chunk",
                    created,
                    model: modelId,
                    choices: [{ delta: { content: thinkParts[1] }, index: 0 }],
                  });
                  yield `data: ${contentChunk}\n\n`;
                }
              } else {
                thinkingContent += thinkingPart;
                const reasoningChunk = JSON.stringify({
                  id: streamId,
                  object: "chat.completion.chunk",
                  created,
                  model: modelId,
                  choices: [{ delta: { reasoning_content: thinkingPart }, index: 0 }],
                });
                yield `data: ${reasoningChunk}\n\n`;
              }
            } else if (content.includes("</think>") && isThinking) {
              const parts = content.split("</think>", 2);
              thinkingContent += parts[0];
              const reasoningChunk = JSON.stringify({
                id: streamId,
                object: "chat.completion.chunk",
                created,
                model: modelId,
                choices: [{ delta: { reasoning_content: parts[0] }, index: 0 }],
              });
              yield `data: ${reasoningChunk}\n\n`;

              isThinking = false;
              if (parts[1]) {
                normalContent += parts[1];
                const contentChunk = JSON.stringify({
                  id: streamId,
                  object: "chat.completion.chunk",
                  created,
                  model: modelId,
                  choices: [{ delta: { content: parts[1] }, index: 0 }],
                });
                yield `data: ${contentChunk}\n\n`;
              }
            } else if (isThinking) {
              thinkingContent += content;
              const reasoningChunk = JSON.stringify({
                id: streamId,
                object: "chat.completion.chunk",
                created,
                model: modelId,
                choices: [{ delta: { reasoning_content: content }, index: 0 }],
              });
              yield `data: ${reasoningChunk}\n\n`;
            } else {
              normalContent += content;
              const contentChunk = JSON.stringify({
                id: streamId,
                object: "chat.completion.chunk",
                created,
                model: modelId,
                choices: [{ delta: { content }, index: 0 }],
              });
              yield `data: ${contentChunk}\n\n`;
            }
          }

          // 更新目标流ID
          targetStreamId = extractRefId(data.next);
        }
      }
    }
  } catch (err) {
    console.error("Stream processing error:", err);
    yield `data: ${JSON.stringify({ error: String(err) })}\n\n`;
  } finally {
    // 发送完成信号
    const done = JSON.stringify({
      id: streamId,
      object: "chat.completion.chunk",
      created,
      model: modelId,
      choices: [{ delta: {}, index: 0, finish_reason: "stop" }],
    });
    yield `data: ${done}\n\ndata: [DONE]\n\n`;

    // 领取奖励
    if (rewardInfo?.unclaimedRewardInfo?.rewardId) {
      try {
        await claimYuppReward(account, rewardInfo.unclaimedRewardInfo.rewardId);
      } catch (err) {
        console.error("Failed to claim reward in background:", err);
      }
    }
  }
}

async function* errorStreamGenerator(msg: string, code = 503) {
  yield `data: ${JSON.stringify({ error: { message: msg, code } })}\n\n`;
  yield "data: [DONE]\n\n";
}

async function buildYuppNonStreamResponse(
  responseLines: AsyncIterable<Uint8Array>,
  modelId: string,
  account: YuppAccount,
): Promise<any> {
  let fullContent = "";
  let fullReasoningContent = "";

  for await (const event of yuppStreamGenerator(responseLines, modelId, account)) {
    if (event.startsWith("data:")) {
      const dataStr = event.slice(5).trim();
      if (dataStr === "[DONE]") break;

      try {
        const data = JSON.parse(dataStr);
        if (data.error) {
          throw new Error(data.error.message || "Unknown error");
        }

        const delta = data.choices?.[0]?.delta || {};
        if (delta.content) {
          fullContent += delta.content;
        }
        if (delta.reasoning_content) {
          fullReasoningContent += delta.reasoning_content;
        }
      } catch (err) {
        if (err instanceof Error && err.message !== "Unexpected end of JSON input") {
          throw err;
        }
      }
    }
  }

  return {
    id: `chatcmpl-${crypto.randomUUID()}`,
    object: "chat.completion",
    created: Math.floor(Date.now() / 1000),
    model: modelId,
    choices: [
      {
        index: 0,
        message: {
          role: "assistant",
          content: fullContent,
          reasoning_content: fullReasoningContent || undefined,
        },
        finish_reason: "stop",
      },
    ],
    usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
  };
}

// ----------------------------------------------------------------------------
// Main request handler
// ----------------------------------------------------------------------------
async function handleRequest(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const { pathname } = url;

  // ------------------------------------------------ GET /debug
  if (pathname === "/debug" && req.method === "GET") {
    const enable = url.searchParams.get("enable");
    if (enable !== null) DEBUG_MODE = enable === "true";
    return jsonResponse({ debug_mode: DEBUG_MODE });
  }

  // ------------------------------------------------ GET /v1/models (requires auth)
  if (pathname === "/v1/models" && req.method === "GET") {
    try {
      requireAuth(req);
    } catch (e) {
      const status = e instanceof Error && e.message === "403" ? 403 : 401;
      return jsonResponse({ error: "unauthorized" }, status);
    }
    return jsonResponse({
      object: "list",
      data: AVAILABLE_MODELS.map((m) => ({
        id: m.id,
        object: "model",
        created: Math.floor(Date.now() / 1000),
        owned_by: m.owned_by,
        name: `${m.label} (${m.name})`,
      })),
    });
  }

  // ------------------------------------------------ GET /models (public)
  if (pathname === "/models" && req.method === "GET") {
    return jsonResponse({
      object: "list",
      data: AVAILABLE_MODELS.map((m) => ({
        id: m.id,
        object: "model",
        created: Math.floor(Date.now() / 1000),
        owned_by: m.owned_by,
        name: `${m.label} (${m.name})`,
      })),
    });
  }

  // ------------------------------------------------ POST /v1/chat/completions
  if (pathname === "/v1/chat/completions" && req.method === "POST") {
    // auth
    try {
      requireAuth(req);
    } catch (e) {
      const status = e instanceof Error && e.message === "403" ? 403 : 401;
      return jsonResponse({ error: "unauthorized" }, status);
    }

    // parse body
    let body: ChatCompletionRequest;
    try {
      body = await req.json();
    } catch {
      return jsonResponse({ error: "invalid json" }, 400);
    }
    if (!body.messages?.length) {
      return jsonResponse({ error: "no messages supplied" }, 400);
    }

    const modelRecord = AVAILABLE_MODELS.find((m) => m.id === body.model);
    if (!modelRecord) {
      return jsonResponse({ error: `model '${body.model}' not found` }, 404);
    }

    const formatted = formatMessagesForYupp(body.messages);
    logDebug(`formatted:\n${formatted.slice(0, 120)}…`);

    // try each account
    for (let attempt = 0; attempt < YUPP_ACCOUNTS.length; ++attempt) {
      const acc = getBestYuppAccount();
      if (!acc) break; // no more accounts

      try {
        const urlUuid = generateUUID();
        const url = `https://yupp.ai/chat/${urlUuid}`;

        const payload = [
          urlUuid,
          generateUUID(),
          formatted,
          "$undefined",
          "$undefined",
          [],
          "$undefined",
          [{ "modelName": modelRecord.name, "promptModifierId": "$undefined" }],
          "text",
          false,
        ];

        const headers = {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
          "Accept": "text/x-component",
          "Accept-Encoding": "gzip, deflate, br, zstd",
          "Content-Type": "application/json",
          "next-action": "7f48888536e2f0c0163640837db291777c39cc40c3",
          "sec-fetch-site": "same-origin",
          "Cookie": `__Secure-yupp.session-token=${acc.token}`,
        };

        logDebug(`Sending request to Yupp.ai with account token ending in ...${acc.token.slice(-4)}`);

        const response = await fetch(url, {
          method: "POST",
          headers,
          body: JSON.stringify(payload),
        });

        if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);

        if (!response.body) throw new Error("No response body");

        if (body.stream) {
          return sseStream(yuppStreamGenerator(response.body, body.model, acc));
        } else {
          const result = await buildYuppNonStreamResponse(response.body, body.model, acc);
          return jsonResponse(result);
        }
      } catch (err) {
        logDebug(`account ${acc.token.slice(-4)} failed: ${err}`);
        acc.error_count++;
        if (err instanceof Error && /401|403/.test(err.message)) {
          acc.is_valid = false;
        }
      }
    }

    // all attempts failed
    if (body.stream) {
      return sseStream(errorStreamGenerator("all yupp attempts failed"));
    }
    return jsonResponse({ error: "all yupp attempts failed" }, 503);
  }

  return jsonResponse({ error: "not found" }, 404);
}

// ----------------------------------------------------------------------------
// Boot
// ----------------------------------------------------------------------------
console.log("Yupp.ai OpenAI Adapter running on http://0.0.0.0:8000");
console.log(`Loaded ${AVAILABLE_MODELS.length} models from ${YUPP_ACCOUNTS.length} Yupp account(s)`);
console.log("Endpoints:");
console.log("  GET  /v1/models (Client API Key Auth)");
console.log("  GET  /models (No Auth)");
console.log("  POST /v1/chat/completions (Client API Key Auth)");
console.log("  GET  /debug?enable=[true|false] (Toggle Debug Mode)");
serve(handleRequest, { port: 8000 });
