#!/usr/bin/env -S deno run -A

/**
 * 简单的测试脚本，用于验证Yupp.ai OpenAI API适配器
 */

const BASE_URL = "http://localhost:8000";
const API_KEY = "sk-dummy-sdfancy"; // 使用默认的测试密钥

async function testModels() {
  console.log("🔍 测试获取模型列表...");
  
  try {
    const response = await fetch(`${BASE_URL}/v1/models`, {
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log("✅ 模型列表获取成功:");
    console.log(`   找到 ${data.data.length} 个模型`);
    data.data.slice(0, 3).forEach((model: any) => {
      console.log(`   - ${model.id} (${model.owned_by})`);
    });
    if (data.data.length > 3) {
      console.log(`   ... 还有 ${data.data.length - 3} 个模型`);
    }
    
    return data.data;
  } catch (error) {
    console.error("❌ 模型列表获取失败:", error);
    return [];
  }
}

async function testChatCompletion(models: any[]) {
  if (models.length === 0) {
    console.log("⚠️  跳过聊天测试 - 没有可用模型");
    return;
  }
  
  const testModel = models[0].id;
  console.log(`\n💬 测试聊天完成 (模型: ${testModel})...`);
  
  try {
    const response = await fetch(`${BASE_URL}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: testModel,
        messages: [
          {
            role: "user",
            content: "请简单介绍一下你自己，用中文回答，不超过50字。"
          }
        ],
        stream: false,
        max_tokens: 100,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log("✅ 聊天完成测试成功:");
    console.log(`   模型: ${data.model}`);
    console.log(`   回答: ${data.choices[0].message.content.slice(0, 100)}...`);
    
    if (data.choices[0].message.reasoning_content) {
      console.log(`   思考过程: ${data.choices[0].message.reasoning_content.slice(0, 50)}...`);
    }
    
  } catch (error) {
    console.error("❌ 聊天完成测试失败:", error);
  }
}

async function testStreamingChat(models: any[]) {
  if (models.length === 0) {
    console.log("⚠️  跳过流式聊天测试 - 没有可用模型");
    return;
  }
  
  const testModel = models[0].id;
  console.log(`\n🌊 测试流式聊天 (模型: ${testModel})...`);
  
  try {
    const response = await fetch(`${BASE_URL}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: testModel,
        messages: [
          {
            role: "user",
            content: "请用中文说一句简单的问候语。"
          }
        ],
        stream: true,
        max_tokens: 50,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    console.log("✅ 流式聊天测试开始:");
    console.log("   响应内容: ", { end: "" });
    
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let content = "";
    
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              console.log("\n   流式响应完成");
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices?.[0]?.delta;
              if (delta?.content) {
                content += delta.content;
                Deno.stdout.writeSync(new TextEncoder().encode(delta.content));
              }
            } catch {
              // 忽略解析错误
            }
          }
        }
      }
    }
    
  } catch (error) {
    console.error("❌ 流式聊天测试失败:", error);
  }
}

async function testDebugEndpoint() {
  console.log("\n🐛 测试调试端点...");
  
  try {
    const response = await fetch(`${BASE_URL}/debug?enable=true`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log("✅ 调试端点测试成功:");
    console.log(`   调试模式: ${data.debug_mode ? '开启' : '关闭'}`);
    
  } catch (error) {
    console.error("❌ 调试端点测试失败:", error);
  }
}

async function main() {
  console.log("🚀 开始测试 Yupp.ai OpenAI API 适配器");
  console.log(`📡 服务器地址: ${BASE_URL}`);
  console.log(`🔑 API密钥: ${API_KEY}`);
  console.log("=" .repeat(50));
  
  // 测试模型列表
  const models = await testModels();
  
  // 测试聊天完成
  await testChatCompletion(models);
  
  // 测试流式聊天
  await testStreamingChat(models);
  
  // 测试调试端点
  await testDebugEndpoint();
  
  console.log("\n" + "=".repeat(50));
  console.log("🎉 测试完成!");
}

if (import.meta.main) {
  main().catch(console.error);
}
