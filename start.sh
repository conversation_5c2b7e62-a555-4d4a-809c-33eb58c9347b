#!/bin/bash

# Yupp.ai OpenAI API Adapter 启动脚本

echo "🚀 启动 Yupp.ai OpenAI API Adapter..."

# 检查Deno是否已安装
if ! command -v deno &> /dev/null; then
    echo "❌ Deno未安装，请先安装Deno:"
    echo "   curl -fsSL https://deno.land/x/install/install.sh | sh"
    exit 1
fi

# 检查主文件是否存在
if [ ! -f "yupp2api.ts" ]; then
    echo "❌ 找不到 yupp2api.ts 文件"
    exit 1
fi

# 设置环境变量
export DEBUG_MODE=${DEBUG_MODE:-false}
export PORT=${PORT:-8000}

echo "📋 配置信息:"
echo "   调试模式: $DEBUG_MODE"
echo "   端口: $PORT"
echo "   文件: yupp2api.ts"

# 启动服务器
echo "🌟 启动服务器..."
deno run -A yupp2api.ts
